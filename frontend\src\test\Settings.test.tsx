import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { vi } from 'vitest';
import { Settings } from '../pages/Settings';

// Mock the AuthContext
const mockAuthContext = {
  currentUser: {
    uid: 'test-user-id',
    email: '<EMAIL>',
    displayName: 'Test User'
  },
  loading: false,
  signup: vi.fn(),
  login: vi.fn(),
  loginWithGoogle: vi.fn(),
  logout: vi.fn()
};

vi.mock('../contexts/AuthContext', () => ({
  useAuth: () => mockAuthContext
}));

// Mock LoadingSpinner
vi.mock('../components/common/LoadingSpinner', () => ({
  LoadingSpinner: () => <div data-testid="loading-spinner">Loading...</div>
}));

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('Settings Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders loading state initially', async () => {
    renderWithRouter(<Settings />);
    
    // Should show loading spinner initially
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('renders settings form after loading', async () => {
    renderWithRouter(<Settings />);
    
    // Wait for loading to complete and settings to render
    await waitFor(() => {
      expect(screen.getByText('Settings')).toBeInTheDocument();
    }, { timeout: 2000 });

    // Check if main sections are rendered
    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(screen.getByText('API Keys')).toBeInTheDocument();
    expect(screen.getByText('Notifications')).toBeInTheDocument();
    expect(screen.getByText('Privacy')).toBeInTheDocument();
    expect(screen.getByText('Billing')).toBeInTheDocument();
  });

  it('displays user information correctly', async () => {
    renderWithRouter(<Settings />);
    
    await waitFor(() => {
      expect(screen.getByText('Settings')).toBeInTheDocument();
    }, { timeout: 2000 });

    // Check if user email is displayed
    await waitFor(() => {
      const emailInput = screen.getByDisplayValue('<EMAIL>');
      expect(emailInput).toBeInTheDocument();
      expect(emailInput).toBeDisabled();
    });
  });

  it('handles user not logged in', () => {
    // Mock no user
    vi.mocked(mockAuthContext).currentUser = null;
    
    renderWithRouter(<Settings />);
    
    // Should not crash and should show loading or appropriate state
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });
});
