import {
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  Timestamp,
  doc,
  getDoc
} from 'firebase/firestore';
import { db } from '../config/firebase';

// Basic analytics interfaces
export interface BasicMetrics {
  totalPrompts: number;
  totalExecutions: number;
  totalDocuments: number;
  avgExecutionTime: number;
  totalCost: number;
  successRate: number;
}

export interface RecentActivity {
  id: string;
  type: 'prompt_created' | 'execution_run' | 'document_uploaded';
  title: string;
  timestamp: Date;
  cost?: number;
  duration?: number;
}

export interface TopPrompt {
  id: string;
  title: string;
  executionCount: number;
  lastUsed: Date;
  avgCost: number;
}

export interface AnalyticsData {
  metrics: BasicMetrics;
  recentActivity: RecentActivity[];
  topPrompts: TopPrompt[];
}

export class AnalyticsService {
  /**
   * Get basic analytics data for a user
   */
  async getUserAnalytics(userId: string): Promise<AnalyticsData> {
    try {
      const [metrics, recentActivity, topPrompts] = await Promise.all([
        this.getBasicMetrics(userId),
        this.getRecentActivity(userId),
        this.getTopPrompts(userId)
      ]);

      return {
        metrics,
        recentActivity,
        topPrompts
      };
    } catch (error) {
      console.error('Error fetching analytics:', error);
      throw new Error('Failed to fetch analytics data');
    }
  }

  /**
   * Get basic metrics for a user
   */
  private async getBasicMetrics(userId: string): Promise<BasicMetrics> {
    try {
      // Get user's prompts
      const promptsRef = collection(db, 'users', userId, 'prompts');
      const promptsSnapshot = await getDocs(promptsRef);
      const totalPrompts = promptsSnapshot.size;

      // Get executions from user's prompts (simpler approach)
      let executions: any[] = [];

      for (const promptDoc of promptsSnapshot.docs) {
        try {
          const executionsRef = collection(db, 'users', userId, 'prompts', promptDoc.id, 'executions');
          const executionsQuery = query(executionsRef, orderBy('timestamp', 'desc'), limit(100));
          const executionsSnapshot = await getDocs(executionsQuery);

          const promptExecutions = executionsSnapshot.docs.map(doc => ({
            id: doc.id,
            promptId: promptDoc.id,
            userId: userId,
            ...doc.data()
          }));

          executions.push(...promptExecutions);
        } catch (error) {
          console.warn(`Error fetching executions for prompt ${promptDoc.id}:`, error);
        }
      }

      const totalExecutions = executions.length;
      
      // Calculate metrics from executions
      let totalCost = 0;
      let totalExecutionTime = 0;
      let successfulExecutions = 0;

      executions.forEach((execution: any) => {
        if (execution.cost) totalCost += execution.cost;
        if (execution.executionTime) totalExecutionTime += execution.executionTime;
        if (execution.status !== 'failed') successfulExecutions++;
      });

      const avgExecutionTime = totalExecutions > 0 ? totalExecutionTime / totalExecutions : 0;
      const successRate = totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0;

      // Get documents count
      const documentsQuery = query(
        collection(db, 'rag_documents'),
        where('uploadedBy', '==', userId)
      );
      const documentsSnapshot = await getDocs(documentsQuery);
      const totalDocuments = documentsSnapshot.size;

      return {
        totalPrompts,
        totalExecutions,
        totalDocuments,
        avgExecutionTime,
        totalCost,
        successRate
      };
    } catch (error) {
      console.error('Error getting basic metrics:', error);
      return {
        totalPrompts: 0,
        totalExecutions: 0,
        totalDocuments: 0,
        avgExecutionTime: 0,
        totalCost: 0,
        successRate: 0
      };
    }
  }

  /**
   * Get recent activity for a user
   */
  private async getRecentActivity(userId: string): Promise<RecentActivity[]> {
    try {
      const activities: RecentActivity[] = [];

      // Get recent executions from user's prompts
      const promptsRef = collection(db, 'users', userId, 'prompts');
      const promptsSnapshot = await getDocs(promptsRef);

      for (const promptDoc of promptsSnapshot.docs) {
        try {
          const executionsRef = collection(db, 'users', userId, 'prompts', promptDoc.id, 'executions');
          const executionsQuery = query(executionsRef, orderBy('timestamp', 'desc'), limit(5));
          const executionsSnapshot = await getDocs(executionsQuery);

          for (const doc of executionsSnapshot.docs) {
            const execution = doc.data();
            activities.push({
              id: doc.id,
              type: 'execution_run',
              title: `Executed prompt: ${promptDoc.data().title || 'Untitled'}`,
              timestamp: execution.timestamp?.toDate() || new Date(),
              cost: execution.cost,
              duration: execution.executionTime
            });
          }
        } catch (error) {
          console.warn(`Error fetching executions for prompt ${promptDoc.id}:`, error);
        }
      }

      // Get recent documents
      const documentsQuery = query(
        collection(db, 'rag_documents'),
        where('uploadedBy', '==', userId),
        orderBy('uploadedAt', 'desc'),
        limit(5)
      );
      
      const documentsSnapshot = await getDocs(documentsQuery);
      
      documentsSnapshot.docs.forEach(doc => {
        const document = doc.data();
        activities.push({
          id: doc.id,
          type: 'document_uploaded',
          title: `Uploaded ${document.filename}`,
          timestamp: document.uploadedAt?.toDate() || new Date()
        });
      });

      // Sort by timestamp and return top 10
      return activities
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, 10);
    } catch (error) {
      console.error('Error getting recent activity:', error);
      return [];
    }
  }

  /**
   * Get top prompts by execution count
   */
  private async getTopPrompts(userId: string): Promise<TopPrompt[]> {
    try {
      const promptsRef = collection(db, 'users', userId, 'prompts');
      const promptsSnapshot = await getDocs(promptsRef);
      
      const promptStats: { [promptId: string]: TopPrompt } = {};

      // Initialize prompt stats
      promptsSnapshot.docs.forEach(doc => {
        const prompt = doc.data();
        promptStats[doc.id] = {
          id: doc.id,
          title: prompt.title || 'Untitled Prompt',
          executionCount: 0,
          lastUsed: new Date(0),
          avgCost: 0
        };
      });

      // Get executions for each prompt
      const promptExecutions: { [promptId: string]: any[] } = {};

      for (const promptDoc of promptsSnapshot.docs) {
        try {
          const executionsRef = collection(db, 'users', userId, 'prompts', promptDoc.id, 'executions');
          const executionsQuery = query(executionsRef, orderBy('timestamp', 'desc'), limit(100));
          const executionsSnapshot = await getDocs(executionsQuery);

          const executions = executionsSnapshot.docs.map(doc => doc.data());
          if (executions.length > 0) {
            promptExecutions[promptDoc.id] = executions;
          }
        } catch (error) {
          console.warn(`Error fetching executions for prompt ${promptDoc.id}:`, error);
        }
      }

      // Calculate stats for each prompt
      Object.keys(promptExecutions).forEach(promptId => {
        const executions = promptExecutions[promptId];
        const totalCost = executions.reduce((sum, exec) => sum + (exec.cost || 0), 0);
        const lastExecution = executions.reduce((latest, exec) => {
          const execTime = exec.timestamp?.toDate() || new Date(0);
          return execTime > latest ? execTime : latest;
        }, new Date(0));

        promptStats[promptId].executionCount = executions.length;
        promptStats[promptId].avgCost = executions.length > 0 ? totalCost / executions.length : 0;
        promptStats[promptId].lastUsed = lastExecution;
      });

      // Return top 5 prompts by execution count
      return Object.values(promptStats)
        .filter(prompt => prompt.executionCount > 0)
        .sort((a, b) => b.executionCount - a.executionCount)
        .slice(0, 5);
    } catch (error) {
      console.error('Error getting top prompts:', error);
      return [];
    }
  }
}

export const analyticsService = new AnalyticsService();
